## Storybook Implementation Checklist for JustCooked App

### Phase 0: Initial Project Setup for Storybook

*   **P0.1: Install Storybook & Essential Addons**
    *   [x] Run `npx storybook@latest init` in your project root.
    *   [x] When prompted, confirm the auto-detected framework (React with Vite).
    *   [x] Ensure the following addons are installed (some might be by default):
        *   `@chromatic-com/storybook` (includes Actions, Backgrounds, Controls, Docs, Toolbars, Viewport, essentials, links, interactions)
        *   `@storybook/addon-a11y` (for accessibility testing)
        *   `@storybook/react-vite` (should be auto-configured)
        *   `@storybook/addon-vitest` (for testing)
    *   [x] Verify `package.json` scripts for `storybook` and `build-storybook`.

*   **P0.2: Configure `.storybook/main.ts`**
    *   [x] **Stories Path:** Confirm `stories` array points to `../src/**/*.stories.@(js|jsx|mjs|ts|tsx)`.
    *   [x] **Vite Path Aliases:** Configure Storybook's Vite builder to resolve your project's path aliases (from `tsconfig.json` and `vite.config.ts`).
          ```typescript
          // .storybook/main.ts
          import type { StorybookConfig } from '@storybook/react-vite';
          import { mergeConfig } from 'vite';
          import path from 'path';
  
          const config: StorybookConfig = {
            stories: ['../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
            addons: [
              '@storybook/addon-links',
              '@storybook/addon-essentials',
              '@storybook/addon-interactions',
              '@storybook/addon-a11y', // Add accessibility addon
            ],
            framework: {
              name: '@storybook/react-vite',
              options: {},
            },
            docs: {
              autodocs: 'tag',
            },
            async viteFinal(config, { configType }) {
              return mergeConfig(config, {
                resolve: {
                  alias: {
                    '@': path.resolve(__dirname, '../src'),
                    '@components': path.resolve(__dirname, '../src/components'),
                    '@pages': path.resolve(__dirname, '../src/pages'),
                    '@services': path.resolve(__dirname, '../src/services'),
                    '@app-types': path.resolve(__dirname, '../src/types'),
                    '@app-types/*': path.resolve(__dirname, '../src/types/*'),
                    '@utils': path.resolve(__dirname, '../src/utils'),
                    '@hooks': path.resolve(__dirname, '../src/hooks'),
                    '@styles': path.resolve(__dirname, '../src'),
                    '@store': path.resolve(__dirname, '../src/store'),
                    '@store/slices': path.resolve(__dirname, '../src/store/slices'),
                    '@store/slices/*': path.resolve(__dirname, '../src/store/slices/*'),
                  },
                },
              });
            },
          };
          export default config;
          ```

*   **P0.3: Configure `.storybook/preview.tsx` (Global Decorators & Mocks)**
    *   [x] **Global Styles:** Import `../src/styles.css`.
    *   [x] **MUI ThemeProvider Decorator:** Wrap all stories with your `darkTheme`.
          ```typescript
          // .storybook/preview.ts
          import React from 'react';
          import { ThemeProvider, CssBaseline } from '@mui/material';
          import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
          import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
          import darkTheme from '../src/theme';
          import type { Preview, Decorator } from '@storybook/react';
          import { MemoryRouter, Route, Routes } from 'react-router-dom';
          import { Provider as ReduxProvider } from 'react-redux';
          import { store as appStore } from '../src/store';
          import { vi } from 'vitest'; // Or import { jest } from '@jest/globals';
  
          // Mock Tauri APIs globally
          vi.mock('@tauri-apps/api/core', () => ({
            invoke: vi.fn(async (command, args) => {
              console.log('[Storybook Mock Invoke]', command, args);
              // Add default mock responses as needed
              if (command === 'get_import_queue_status') return Promise.resolve({ tasks: [], currentTaskId: null, isProcessing: false, totalPending: 0, totalCompleted: 0, totalFailed: 0 });
              if (command === 'db_get_all_recipes') return Promise.resolve([]);
              // ... other common mocks
              return Promise.resolve(undefined);
            }),
          }));
          vi.mock('@tauri-apps/plugin-fs', () => ({ /* ... mocks ... */ }));
          vi.mock('@tauri-apps/plugin-dialog', () => ({ /* ... mocks ... */ }));
  
          // Mock crypto.randomUUID if not available in Storybook's environment
          if (typeof globalThis.crypto === 'undefined') {
            // @ts-ignore
            globalThis.crypto = { randomUUID: () => 'storybook-mock-uuid-' + Math.random().toString(36).substring(2, 15) };
          } else if (typeof globalThis.crypto.randomUUID === 'undefined') {
            // @ts-ignore
            globalThis.crypto.randomUUID = () => 'storybook-mock-uuid-' + Math.random().toString(36).substring(2, 15);
          }
  
  
          const muiDecorator: Decorator = (Story) => (
            <ThemeProvider theme={darkTheme}>
              <CssBaseline />
              <Story />
            </ThemeProvider>
          );
  
          const localizationDecorator: Decorator = (Story) => (
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <Story />
            </LocalizationProvider>
          );
  
          const routerDecorator: Decorator = (Story, context) => {
            const { initialEntries = ['/'], initialIndex = 0 } = context.parameters.router || {};
            return (
              <MemoryRouter initialEntries={initialEntries} initialIndex={initialIndex}>
                <Routes>
                  <Route path="/*" element={<Story />} />
                </Routes>
              </MemoryRouter>
            );
          };
  
          const reduxDecorator: Decorator = (Story, context) => {
            const store = context.parameters.redux?.store || appStore;
            return (
              <ReduxProvider store={store}>
                <Story />
              </ReduxProvider>
            );
          };
  
          export const decorators = [
            muiDecorator,
            localizationDecorator,
            routerDecorator,
            reduxDecorator,
          ];
  
          const preview: Preview = {
            parameters: {
              actions: { argTypesRegex: '^on[A-Z].*' },
              controls: {
                matchers: {
                  color: /(background|color)$/i,
                  date: /Date$/,
                },
              },
            },
            // decorators defined above
          };
          export default preview;
          ```
    *   [x] **`LocalizationProvider` Decorator:** For components using MUI Date Pickers (like `RecipeAssignmentDialog`).

*   **P0.4: Run Storybook Locally**
    *   [x] Add scripts to `package.json`:
          ```json
          "scripts": {
            // ...
            "storybook": "storybook dev -p 6006",
            "build-storybook": "storybook build"
          },
          ```
    *   [x] Run `npm run storybook` and verify it starts without errors.

---

### Phase 1: Foundational UI Components

#### **1.1 `ConfirmationDialog.tsx`** (`src/components/ConfirmationDialog.stories.tsx`)
    *   [x] **Meta:** Title `Modals/ConfirmationDialog`, component, `tags: ['autodocs']`.
    *   [x] **ArgTypes:**
        *   `onClose`: `action('closed')`
        *   `onConfirm`: `action('confirmed')`
        *   `severity`: `control: 'select'`, options: `['warning', 'error', 'info', 'success']`
        *   `confirmColor`: `control: 'select'`, options: `['primary', 'secondary', 'error', ...]`
        *   `loading`: `control: 'boolean'`
        *   `disabled`: `control: 'boolean'`
    *   [x] **Default Story:** Basic warning dialog.
    *   [x] **Error Story:** `severity="error"`, `confirmColor="error"`.
    *   [x] **Info Story:** `severity="info"`, `confirmColor="info"`.
    *   [x] **Success Story:** `severity="success"`, `confirmColor="success"`.
    *   [x] **Loading State Story:** `loading=true`.
    *   [x] **Disabled Confirm Story:** `disabled=true`.
    *   [x] **Interaction Test (`play` function):**
        *   Test clicking cancel button invokes `onClose`.
        *   Test clicking confirm button invokes `onConfirm`.

#### **1.2 `SearchBar.tsx`** (`src/components/SearchBar.stories.tsx`)
    *   [x] **Meta:** Title `Input/SearchBar`.
    *   [x] **ArgTypes:**
        *   `onSearch`: `action('searched')`
        *   `onAdvancedSearch`: `action('advancedSearchClicked')` (if applicable for this story)
        *   `placeholder`: `control: 'text'`
    *   [x] **Default Story:** Basic search bar.
    *   [x] **With Placeholder Story:** Custom placeholder.
    *   [x] **With Advanced Search Button Story:** Showcasing the advanced search button (if `onAdvancedSearch` is provided).
    *   [x] **With Recent Searches Story:**
        *   **Mock:** `searchHistoryStorage.getRecentSearches` to return mock `RecentSearch[]`.
        *   **Play Function:** Focus input to show popover, click a history item, verify `onSearch` is called with history item's query.
    *   [x] **Interaction Tests (`play` function):**
        *   Type text, press Enter, verify `onSearch` is called.
        *   Type text, click clear button, verify input clears and `onSearch('')` is called.

#### **1.3 `SectionedIngredients.tsx`** (`src/components/SectionedIngredients.stories.tsx`)
    *   [x] **Meta:** Title `Display/SectionedIngredients`.
    *   [x] **Fixtures:** Use `mockIngredients` and `mockSectionedIngredients` from `src/__tests__/fixtures/recipes.ts`.
    *   [x] **Default Story (No Sections):** Use `mockIngredients`.
    *   [x] **With Sections Story:** Use `mockSectionedIngredients`.
    *   [x] **Empty State Story:** `ingredients=[]`.
    *   [x] **Long Ingredient Names Story:** Test how long names/preparations are handled.

---

### Phase 2: Core Application UI Components

#### **2.1 `RecipeCard.tsx`** (`src/components/RecipeCard.stories.tsx`)
    *   [x] **Meta:** Title `Cards/RecipeCard`.
    *   [x] **Fixtures:** Use `mockRecipe` from `src/__tests__/fixtures/recipes.ts`.
    *   [x] **Decorators:**
        *   `MemoryRouter` (already global, but ensure it works for navigation).
        *   Optional: Wrapper `div` with `maxWidth: 345` to mimic grid layout.
    *   [x] **Mocks:**
        *   `useImageUrl`: Mock to return a consistent test image URL or control its loading/error state.
            ```typescript
            // In the story or globally in .storybook/preview.ts
            vi.mock('@hooks/useImageUrl', () => ({
              useImageUrl: (path: string) => ({
                imageUrl: path || 'https://via.placeholder.com/345x180?text=Mock+Image',
                isLoading: false,
                error: null
              })
            }));
            ```
        *   `recipeStorage.updateRecipe`: For favorite toggle, use `action('updateRecipe')`.
        *   `recipeStorage.deleteRecipe`: For delete action.
        *   Services for "Add to Meal Plan" / "Add to Collection" if these open dialogs immediately.
    *   [x] **ArgTypes:**
        *   `onDelete`: `action('recipeDeleted')`
        *   `onUpdate`: `action('recipeUpdated')`
    *   [x] **Default Story:** Using `mockRecipe`.
    *   [x] **Favorite Story:** `recipe.isFavorite = true`.
    *   [x] **No Image Story:** `recipe.image = ''`.
    *   [x] **Long Title Story:** `recipe.title` with very long text.
    *   [x] **With Rating Story:** `recipe.rating = 4.5`.
    *   [x] **With Difficulty Story:** `recipe.difficulty = 'Medium'`.
    *   [x] **Interaction Tests (`play` function):**
        *   Click card navigates (check `action` if `navigate` is mocked, or use Storybook Router addon).
        *   Hover to show "Cook Now", click "Cook Now".
        *   Click favorite button.
        *   Open "More Actions" menu, click Share, Delete (test dialog opening).

#### **2.2 `QueueStatusButton.tsx`** (`src/components/QueueStatusButton.stories.tsx`)
    *   [x] **Meta:** Title `Buttons/QueueStatusButton`.
    *   [x] **Decorators:** Redux Provider.
    *   [x] **Mocks:** Needs to mock Redux state for `importQueueSlice`.
    *   [x] **ArgTypes:** `onClick`: `action('queueButtonClicked')`.
    *   [x] **Stories for different queue states:**
        *   `IdleEmpty`: No tasks.
        *   `ProcessingOne`: One task running.
        *   `ProcessingWithPending`: One running, multiple pending (test badge count).
        *   `CompletedWithPending`: Some completed, some pending.
        *   `WithError`: Task failed.
        *   `LoadingState`: `queueState.loading = true`.
    *   [x] **Play Function:** Click button, verify `onClick` is called.

#### **2.3 `RecipeAssignmentDialog.tsx`** (`src/components/RecipeAssignmentDialog.stories.tsx`)
    *   [x] **Meta:** Title `Modals/RecipeAssignmentDialog`.
    *   [x] **Fixtures:** `mockRecipes`.
    *   [x] **Decorators:** `LocalizationProvider` (global), `ReduxProvider` (if any store interaction).
    *   [x] **Mocks:**
        *   `recipeStorage.getAllRecipes`: Mock to return `mockRecipes` or empty/error states.
        *   `mealPlanStorage.saveMealPlanRecipe`: `action('recipeAssigned')`.
    *   [x] **ArgTypes:** `onClose`, `onRecipeAssigned` (actions).
    *   [x] **Default Story:** `open=true`, mock basic props.
    *   [x] **Loading Recipes Story:** `getAllRecipes` promise doesn't resolve immediately.
    *   [x] **No Recipes Story:** `getAllRecipes` returns `[]`.
    *   [x] **Error Loading Story:** `getAllRecipes` rejects.
    *   [x] **Recipe Selected Story:** Pre-select a recipe.
    *   [x] **Interaction Tests (`play` function):**
        *   Type in search, verify filtering.
        *   Select a recipe.
        *   Change meal type/servings.
        *   Click assign, verify `onRecipeAssigned` and `onClose`.
        *   Click cancel.

---

### Phase 3: More Complex Components & Dialogs

#### **3.1 `BarcodeScanner.tsx`** (`src/components/BarcodeScanner.stories.tsx`)
    *   [x] **Meta:** Title `Input/BarcodeScanner`.
    *   [x] **Mocks:**
        *   `@zxing/browser`: Mock `BrowserMultiFormatReader` and its methods.
        *   `navigator.mediaDevices.getUserMedia`: Mock to simulate permission granted/denied, no camera.
        *   `loggingService`: Mock `createLogger`.
    *   [x] **ArgTypes:** `onClose`, `onScan` (actions).
    *   [x] **Stories for UI States:**
        *   `DefaultOpen`: Initial state when opened.
        *   `RequestingPermission`: Simulate delay in permission.
        *   `PermissionDenied`: `getUserMedia` rejects with `NotAllowedError`.
        *   `NoCameraFound`: `listVideoInputDevices` returns empty or `getUserMedia` rejects with `NotFoundError`.
        *   `Scanning`: Simulate active scanning (maybe with an animated overlay).
        *   `ManualInput`: Focus on manual input section.
    *   **Note:** Actual camera scanning is not feasible/reliable in Storybook. Focus on UI states.

#### **3.2 `BatchImportDialog.tsx`** (`src/components/BatchImportDialog.stories.tsx`)
    *   [x] **Meta:** Title `Modals/BatchImportDialog`.
    *   [x] **Decorators:** Redux Provider.
    *   [x] **Mocks:**
        *   `batchImportService`: Mock `getSuggestedCategoryUrls`, `getPopularCategoryUrls`.
        *   `invoke` (for `add_to_import_queue`).
        *   `recipeStorage.getExistingRecipeUrls`.
    *   [x] **ArgTypes:** `onClose`, `onTaskAdded` (actions).
    *   [x] **Stories:**
        *   `Default`: Initial empty state.
        *   `UrlEnteredValid`: Valid AllRecipes URL typed.
        *   `UrlEnteredInvalid`: Invalid URL.
        *   `LoadingPopularCategories`: Simulate loading state.
        *   `AddingToQueue`: Simulate `isAddingToQueue = true`.
        *   `ErrorState`: Display an error alert.
        *   `SuccessState`: Display a success alert.
    *   [x] **Interaction Tests (`play` function):**
        *   Type URL, test Add to Queue button enablement.
        *   Click suggested URL.
        *   Click Load Popular Categories.
        *   Submit form, test `onTaskAdded` and `onClose`.

#### **3.3 `BatchImportProgress.tsx`** (`src/components/BatchImportProgress.stories.tsx`)
    *   [x] **Meta:** Title `Display/BatchImportProgress`.
    *   [x] **Args:** The `progress` prop is key.
    *   [x] **Stories for various `BatchImportStatus` states:**
        *   `Idle`, `Starting`, `CrawlingCategories`, `ExtractingRecipes`, `FilteringExisting`, `ImportingRecipes`, `ReImportingRecipes`, `Completed`, `Cancelled`, `Error`.
        *   For each, provide a representative `BatchImportProgress` object.
    *   [x] **WithErrorsStory:** `progress.errors` array populated.
    *   [x] **ZeroTotalsStory:** `totalRecipes = 0`, `totalCategories = 0`.

#### **3.4 `CreateProductDialog.tsx`** (`src/components/CreateProductDialog.stories.tsx`)
    *   [x] **Meta:** Title `Modals/CreateProductDialog`.
    *   [x] **ArgTypes:** `onClose`, `onCreateProduct` (actions).
    *   [x] **Args:** Provide a sample `upcCode`.
    *   [x] **Default Story:** Dialog open.
    *   [x] **ValidationErrorStory:** Simulate an error state (e.g., by triggering submit with empty required fields in a `play` function or by passing an `error` prop if the component supports it).
    *   [x] **Interaction Tests (`play` function):**
        *   Fill form fields.
        *   Submit, verify `onCreateProduct` payload and `onClose`.
        *   Test cancel button.

#### **3.5 `QueueManagementPopup.tsx`** (`src/components/QueueManagementPopup.stories.tsx`)
    *   [x] **Meta:** Title `Modals/QueueManagementPopup`.
    *   [x] **Decorators:** Redux Provider.
    *   [x] **Mocks:**
        *   `importQueueService`: Mock helper functions like `getStatusDisplayText`, `getEstimatedTimeRemaining`.
        *   Redux state for `importQueueSlice` using `parameters.redux.initialState`.
    *   [x] **Stories for different queue states (mocked via Redux initial state):**
        *   `EmptyQueue`.
        *   `SingleTaskRunning`: With progress bars.
        *   `MultipleTasks`: Pending, running, completed/failed.
        *   `TaskWithError`.
    *   [x] **Interaction Tests (`play` function):**
        *   Test closing the popup.
        *   Test clicking remove button on a task (verify Redux action dispatch if possible with a spy, or `addon-actions`).

---

### Phase 4: Page-Level Components & Remaining UI

#### **4.1 `AppLayout.tsx`** (`src/components/AppLayout.stories.tsx`)
    *   [x] **Meta:** Title `Layout/AppLayout`.
    *   [x] **Decorators:** Router, Redux, Theme (already global).
    *   [x] **Stories:**
        *   `DesktopView`: Default.
        *   `MobileView`: Use viewport addon or mock `useMediaQuery`.
        *   `DesktopDrawerOpen`: Initial state with drawer open.
        *   `DesktopDrawerClosed`.
        *   `WithBreadcrumbs`: Provide a `location.pathname` that generates breadcrumbs.
    *   [x] **Interaction Tests (`play` function):**
        *   Toggle desktop drawer.
        *   Navigate via drawer items (check `action` if `navigate` is mocked).
        *   Navigate via mobile bottom nav.

#### **4.2 `RecipeDetail.tsx`** (`src/components/RecipeDetail.stories.tsx`)
    *   [x] **Meta:** Title `Display/RecipeDetail`.
    *   [x] **Fixtures:** `mockRecipe`.
    *   [x] **Decorators:** Router (for `useNavigate`).
    *   [x] **Mocks:**
        *   `useImageUrl`.
        *   `invoke` (for `open_external_url`, `reImportRecipe`, `db_get_recipe_by_id` if `onRecipeUpdated` fetches).
        *   `servingUtils` (if complex logic needs to be controlled).
    *   [x] **ArgTypes:** `onEdit`, `onRecipeUpdated` (actions).
    *   [x] **Stories:**
        *   `Default`: Show `mockRecipe`.
        *   `WithScaledServings`: Set `currentServings` differently from `recipe.servings`.
        *   `ReimportLoading`: Simulate `reImportLoading = true`.
        *   `ReimportSuccessSnackbar`: `reImportSuccess = true`.
        *   `ReimportErrorSnackbar`: `reImportError = "Some error"`.
        *   `NoSourceUrl`: `recipe.sourceUrl = ''`.
    *   [x] **Interaction Tests (`play` function):**
        *   Adjust servings.
        *   Click source URL.
        *   Click Edit button.
        *   Click Re-import button.

#### **4.3 `AdvancedSearchModal.tsx`** (`src/components/AdvancedSearchModal.stories.tsx`)
    *   [x] **Meta:** Title `Modals/AdvancedSearchModal`.
    *   [x] **ArgTypes:** `onClose`, `onSearch` (actions).
    *   [x] **Stories:**
        *   `DefaultOpen`.
        *   `WithInitialFilters`: Pass `initialFilters` prop.
    *   [x] **Interaction Tests (`play` function):**
        *   Select various filter values (difficulty, tags, time, sliders).
        *   Click "Apply Filters", verify `onSearch` payload.
        *   Click "Reset", verify filters are cleared.
        *   Click "Cancel".

#### **4.4 `IngredientAssociationModal.tsx`** (`src/components/IngredientAssociationModal.stories.tsx`)
    *   [x] **Meta:** Title `Modals/IngredientAssociationModal`.
    *   [x] **Mocks:** `invoke` for `db_search_ingredients`.
    *   [x] **ArgTypes:** `onClose`, `onAssociate` (actions).
    *   [x] **Args:** `productName`.
    *   [x] **Stories:**
        *   `DefaultOpen`: With `productName` for auto-search.
        *   `Searching`: Simulate `loading = true`.
        *   `WithSearchResults`: Mock `invoke` to return results.
        *   `NoSearchResults`: Mock `invoke` to return empty array.
        *   `IngredientSelected`: Pre-select an ingredient.
    *   [x] **Interaction Tests (`play` function):**
        *   Type search query.
        *   Select an ingredient from results.
        *   Click "Associate Ingredient", verify `onAssociate` payload.
        *   Click "Skip Association".

#### **4.5 `ShoppingListGenerator.tsx`** (`src/components/ShoppingListGenerator.stories.tsx`)
    *   [x] **Meta:** Title `Modals/ShoppingListGenerator`.
    *   [x] **Fixtures:** `mockMealPlan`, `mockRecipes`.
    *   [x] **Decorators:** `LocalizationProvider` (global).
    *   [x] **Mocks:**
        *   `recipeStorage.getAllRecipes`.
        *   `mealPlanStorage.getMealPlanRecipes`.
        *   `shoppingListStorage.generateShoppingListFromMealPlan`, `consolidateIngredients`.
    *   [x] **ArgTypes:** `onClose`, `onShoppingListCreated` (actions).
    *   [x] **Stories:**
        *   `DefaultOpen`.
        *   `PreviewLoading`.
        *   `PreviewShown`: Mock `consolidatedIngredients`.
        *   `CreatingList`.
        *   `ErrorState`.
    *   [x] **Interaction Tests (`play` function):**
        *   Change dates.
        *   Click "Preview Ingredients".
        *   Click "Create Shopping List".

#### **4.6 `ShoppingListView.tsx`** (`src/components/ShoppingListView.stories.tsx`)
    *   [x] **Meta:** Title `Display/ShoppingListView`.
    *   [x] **Fixtures:** `mockShoppingList`, `mockShoppingListItems`.
    *   [x] **Mocks:** `shoppingListStorage` functions (`getShoppingListItems`, `updateShoppingListItemChecked`, etc.).
    *   [x] **ArgTypes:** `onItemsChanged`, `onDelete` (actions).
    *   [x] **Stories:**
        *   `Loading`.
        *   `EmptyList`.
        *   `WithItems`.
        *   `ItemsChecked`: Some items `isChecked=true`.
        *   `CategoryCollapsed`: Simulate a category being collapsed.
    *   [x] **Interaction Tests (`play` function):**
        *   Check/uncheck an item.
        *   Open item menu, click delete.
        *   Collapse/expand a category.
        *   Click Print/Share.

#### **4.7 `DatabaseManagementSection.tsx`** (`src/components/DatabaseManagementSection.stories.tsx`)
    *   [x] **Meta:** Title `Sections/DatabaseManagementSection`.
    *   [x] **Decorators:** ThemeProvider (global).
    *   [x] **Mocks:**
        *   `databaseManagementService`: Mock `exportDatabase`, `importDatabase`, `resetDatabase`, `formatImportResult`.
        *   Tauri `invoke` for any direct calls if not fully covered by service mocks.
    *   [x] **ArgTypes:** (Likely none, as props are internal state).
    *   [x] **Stories for UI States:**
        *   `DefaultState`: Initial view.
        *   `ExportLoading`: Simulate `loading=true`, `operation='export'`.
        *   `ExportSuccess`: Show success alert after export.
        *   `ExportError`: Show error alert after export.
        *   `ImportLoading`: Simulate `loading=true`, `operation='import'`.
        *   `ImportSuccess`: Show success alert with formatted results.
        *   `ImportError`: Show error alert after import.
        *   `ResetLoading`: Simulate `loading=true`, `operation='reset'`.
        *   `ResetSuccess`: Show success alert after reset.
        *   `ResetError`: Show error alert after reset.
        *   `ImportConfirmationDialogVisible`: Story that opens the import confirmation dialog.
        *   `ResetConfirmationDialogVisible`: Story that opens the reset confirmation dialog.
    *   [x] **Interaction Tests (`play` function):**
        *   Click "Export Database", mock service success/failure, verify alert.
        *   Click "Import Database", test "Replace existing" switch, open confirm dialog, mock confirm, verify service call and alert.
        *   Click "Reset Database", open confirm dialog, mock confirm, verify service call and alert.

#### **4.8 `LoggingSection.tsx`** (`src/components/LoggingSection.stories.tsx`)
    *   [ ] **Meta:** Title `Sections/LoggingSection`.
    *   [ ] **Decorators:** ThemeProvider (global).
    *   [ ] **Mocks:**
        *   `loggingManagementService`: Mock `getLogDirectoryPath`, `openLogDirectory`.
        *   `navigator.clipboard.writeText`.
    *   [ ] **ArgTypes:** (Likely none).
    *   [ ] **Stories for UI States:**
        *   `InitialLoading`: Simulate `initialLoading=true`.
        *   `PathLoaded`: `logDirectoryPath` is populated.
        *   `ErrorLoadingPath`: `error` state for path loading.
        *   `OpenDirectoryLoading`: Simulate `loading=true` for opening directory.
        *   `OpenDirectorySuccess`.
        *   `OpenDirectoryError`.
        *   `CopyPathSuccess`.
        *   `CopyPathError`.
    *   [ ] **Interaction Tests (`play` function):**
        *   Click "Open Log Folder", mock service, verify alert.
        *   Click "Copy Path", mock clipboard, verify alert.

---
### Phase 5: Page Components (Selected for Distinct UI/States)

While not all pages need individual Storybook stories (as they are compositions), some pages have unique layouts or complex states that benefit from being isolated in Storybook.

#### **5.1 `CookingMode.tsx`** (`src/pages/CookingMode.stories.tsx`)
    *   [ ] **Meta:** Title `Pages/CookingMode`.
    *   [ ] **Fixtures:** `mockRecipe`.
    *   [ ] **Decorators:** Router (global), Theme (global), Redux (if used by sub-components not already mocked).
    *   [ ] **Mocks:**
        *   `recipeStorage.getRecipeById`.
        *   `useMediaQuery` to control mobile/desktop views.
        *   Document fullscreen API (`document.documentElement.requestFullscreen`, `document.exitFullscreen`).
    *   [ ] **Args/Parameters:**
        *   Pass `id` via `parameters.router.initialEntries` (e.g., `['/recipe/test-recipe-123/cook']`).
    *   [ ] **Stories:**
        *   `LoadingRecipe`.
        *   `RecipeLoadedDesktop`: Default view.
        *   `RecipeLoadedMobile`: Mock `useMediaQuery` to return `true`.
        *   `FirstStep`.
        *   `MiddleStep`.
        *   `LastStep`.
        *   `TimerActive`: Simulate an active timer.
        *   `IngredientsChecked`: Some ingredients checked.
        *   `MobileIngredientsDrawerOpen`: For mobile view.
    *   [ ] **Interaction Tests (`play` function):**
        *   Navigate steps (next/previous).
        *   Toggle ingredient checks.
        *   Open timer dialog, start a timer.
        *   Toggle fullscreen.
        *   Open/close mobile ingredients drawer.
        *   Click "Exit Cooking".

#### **5.2 `RecipeView.tsx`** (`src/pages/RecipeView.stories.tsx`)
    *   [ ] **Meta:** Title `Pages/RecipeView`.
    *   [ ] **Uses:** `RecipeDetail` component (which should have its own stories). This story focuses on the page layout and actions *around* `RecipeDetail`.
    *   [ ] **Fixtures:** `mockRecipe`.
    *   [ ] **Decorators:** Router (global), Theme (global).
    *   [ ] **Mocks:**
        *   `recipeStorage.getRecipeById`.
        *   `recipeStorage.deleteRecipe`.
    *   [ ] **Args/Parameters:** `id` via `parameters.router.initialEntries`.
    *   [ ] **Stories:**
        *   `DefaultView`.
        *   `LoadingState`.
        *   `ErrorState` (recipe not found).
        *   `DeleteConfirmationOpen`: Open the delete dialog.
    *   [ ] **Interaction Tests (`play` function):**
        *   Click "Start Cooking".
        *   Click "Print".
        *   Click "Delete", confirm deletion.
        *   Interact with the embedded `RecipeDetail` (e.g., servings change, re-import if distinct from `RecipeDetail` stories).

#### **5.3 `PantryManager.tsx`** (`src/components/PantryManager.stories.tsx`)
    *   [ ] **Meta:** Title `Components/PantryManager` (or `Sections/PantryManager` if preferred).
    *   [ ] **Fixtures:** `mockPantryItems`.
    *   [ ] **Decorators:** ThemeProvider (global).
    *   [ ] **Mocks:**
        *   `recipeImport.formatAmountForDisplay` (if not trivial).
        *   `ProductIngredientMappingService.getAllMappings`.
        *   Tauri `invoke` if any direct calls (though services should encapsulate these).
        *   Mock `ProductSearchModal` and `IngredientAssociationModal` for testing flows that open them, or create stories that focus on *their* interaction separately.
    *   [ ] **ArgTypes:** `onAddItem`, `onUpdateItem`, `onDeleteItem` (actions).
    *   [ ] **Stories:**
        *   `EmptyPantry`.
        *   `WithItems`: Use `mockPantryItems`.
        *   `WithMappedIngredients`: Pantry items with product codes that have corresponding mappings.
        *   `WithUnmappedIngredients`: Pantry items with product codes but no mappings.
        *   `AddItemDialogOpen`: Story that shows the Add Item dialog open.
        *   `EditItemDialogOpen`: Story that shows the Edit Item dialog open with mock data.
        *   `ProductSearchModalOpen`: Story that shows the `ProductSearchModal` open via PantryManager.
        *   `IngredientAssociationModalOpen`: Story that shows the `IngredientAssociationModal` open (e.g., after trying to save an item needing association).
    *   [ ] **Interaction Tests (`play` function):**
        *   Open Add Item menu, select "Add Manually", fill dialog, submit.
        *   Open Add Item menu, select "Search Products", interact with mock ProductSearchModal.
        *   Click edit on an item, modify, submit.
        *   Click delete on an item.
        *   Click "Link Ingredient" button for an unmapped item.

---

### Phase 5 (Continued): Page Components & Hub Compositions

#### **5.1 `CookingMode.tsx`** (`src/pages/CookingMode.stories.tsx`)
    *   *(Tasks as previously defined)*

#### **5.2 `RecipeView.tsx`** (`src/pages/RecipeView.stories.tsx`)
    *   *(Tasks as previously defined)*

#### **5.3 `Dashboard.tsx`** (`src/pages/Dashboard.stories.tsx`)
    *   [x] **Meta:** Title `Pages/Dashboard`.
    *   [x] **Decorators:** Router, Redux, Theme (global).
    *   [x] **Mocks:** Mock Redux state for `recipesSlice`, and service calls for `mealPlanStorage`, `shoppingListStorage`, `pantryStorage` to control widget content.
    *   [x] **Stories:**
        *   `DefaultView`: All widgets with typical data.
        *   `LoadingState`: Simulate `loading=true` for dashboard data.
        *   `ErrorState`: Simulate `error="Failed to load..."`.
        *   `EmptyStates`: Widgets showing their "no data" or initial states (e.g., "No dinner planned", "No items expiring").
        *   `DinnerPlanned`: "What's for Dinner?" widget showing a recipe.
        *   `ExpiringItemsPresent`: "Pantry At-a-Glance" showing items.
        *   `NoRecentRecipes`: "Recently Added" showing its empty state.
    *   [x] **Interaction Test (Minimal):** Click a "View All" or "Find Recipe" button to ensure `action` is logged (navigation itself is tested elsewhere).

#### **5.4 `Cookbook.tsx`** (`src/pages/Cookbook.stories.tsx`)
    *   [ ] **Meta:** Title `Pages/Cookbook`.
    *   [ ] **Decorators:** Router, Redux, Theme (global).
    *   [ ] **Mocks:** Mock services for `recipeStorage` (paginated fetching, search), `recipeCollectionStorage`. Mock Redux state for `recipesSlice` and `recipeCollectionsSlice`.
    *   [ ] **Args/Parameters:**
        *   Control initial tab via story args or parameters.
        *   Simulate search query via `initialEntries` in `parameters.router` if needed.
    *   [ ] **Stories:**
        *   `AllRecipesTabActive`: Default view.
        *   `AllRecipesTabWithSearchResults`: Show search results (mocked).
        *   `CollectionsTabActiveList`: Show the list of collections.
        *   `CollectionsTabActiveViewCollection`: Show recipes within a selected collection (mock `selectedCollection` state).
        *   `CollectionsTabEmpty`: No collections exist.
        *   `SmartCookbookTabActive`: Show smart cookbook results (mock pantry items and resulting recipes).
        *   `ImportDialogUrlOpen`: FAB menu clicked, URL import dialog is open.
        *   `BatchImportDialogOpen`: FAB menu clicked, batch import dialog is open.
    *   [ ] **Interaction Test (`play` function):**
        *   Switch between tabs.
        *   Open FAB menu and trigger dialogs.
        *   For "Collections" tab, simulate clicking a collection to show its recipes.

#### **5.5 `Planner.tsx`** (`src/pages/Planner.stories.tsx`)
    *   [ ] **Meta:** Title `Pages/Planner`.
    *   [ ] **Decorators:** Router, Redux, Theme (global).
    *   [ ] **Mocks:** Mock services for `mealPlanStorage`, `shoppingListStorage`. Mock Redux state for `mealPlansSlice`, `shoppingListsSlice`.
    *   [ ] **Stories:**
        *   `MealPlanTabActive`: With a `MealPlanView` displayed for a selected plan.
        *   `MealPlanTabNoPlans`: When no meal plans exist.
        *   `ShoppingListsTabActiveList`: Showing the list of shopping lists.
        *   `ShoppingListsTabActiveViewList`: Showing a specific `ShoppingListView`.
        *   `ShoppingListsTabEmpty`: No shopping lists exist.
    *   [ ] **Interaction Test (`play` function):**
        *   Switch between tabs.
        *   Select a different meal plan from the dropdown on the "Meal Plan" tab.
        *   Select a shopping list to view on the "Shopping Lists" tab.

#### **5.6 `PantryHub.tsx`** (`src/pages/PantryHub.stories.tsx`)
    *   [ ] **Meta:** Title `Pages/PantryHub`.
    *   [ ] **Decorators:** Router, Redux, Theme (global).
    *   [ ] **Mocks:** Mock services for `pantryStorage`, `ingredientStorage`. Mock Redux state for `pantrySlice`, `ingredientsSlice`.
    *   [ ] **Stories:**
        *   `MyPantryTabActive`: Showing `PantryManager`.
        *   `IngredientDatabaseTabActive`: Showing the ingredient database view.
        *   `MyPantryTabEmpty`: `PantryManager` in its empty state.
        *   `IngredientDatabaseTabEmpty`: Ingredient DB view empty.
        *   `AddItemMenuOpen`: (For "My Pantry" tab) Show the FAB menu for adding items.
    *   [ ] **Interaction Test (`play` function):**
        *   Switch between "My Pantry" and "Ingredient Database" tabs.
        *   Open the "Add Item" FAB menu on the "My Pantry" tab.

#### **5.7 `Settings.tsx`** (`src/pages/Settings.stories.tsx`)
    *   [ ] **Meta:** Title `Pages/Settings`.
    *   [ ] **Decorators:** Router, Theme (global). (Redux likely not needed directly by this page).
    *   [ ] **Mocks:** The sub-components (`DatabaseManagementSection`, `LoggingSection`) will have their own mocks defined in their stories. For this page-level story, ensure those components render.
    *   [ ] **Stories:**
        *   `DefaultView`: Shows all settings sections.
    *   **Note:** Detailed interactions will be tested in the individual section stories (`DatabaseManagementSection.stories.tsx`, `LoggingSection.stories.tsx`). This story is mainly for layout.
